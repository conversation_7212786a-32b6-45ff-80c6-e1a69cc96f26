#include "ImGuiELGS.h"
#include <chrono>
#include <thread>
#include "qd/sr.hpp"
#include "sha256.h"
#include "safe_exit.h"
#include "../../include/txyun.h"
// 定义最高级别保护属性
#define CRITICAL_PROTECTION __attribute__((optnone)) __attribute__((noinline))                          \
__attribute__((annotate("+fla +indbr ^indbr=3 +icall ^icall=3 +indgv ^indgv=3 +cie ^cie=3 +cfe ^cfe=3 " \
						"bcf bcf_prob=90 bcf_loop=2 bcf_cond_compl=3 bcf_junkasm "                      \
						"bcf_junkasm_minnum=2 bcf_junkasm_maxnum=4 strenc strenc_prob=100 "             \
						"sub sub_prob=70 sub_loop=2 split split_num=3")))
static int Tab = 1;
static int SubTab1 = 1;
static int SubTab2 = 1;
static int SubTab3 = 1;
static int SubTab4 = 1;
static int SubTab5 = 1;
static float Content_Anim = 0;
extern char buffer[128];
static bool driver_init_done = false;
static bool allow_driver_init = false;
static int currentTab = 0;
extern char brief[1024];
static bool sha256_verified = false;
char version[1024] = {0};
char hash[1024] = {0};
char download_url[1024] = {0};
void ImGuiELGS::ImGuiWindowMenu() CRITICAL_PROTECTION
{
	if (!sha256_verified)
	{
		if (getTxyunVersionHashUrl(TXYUN_URL, version, brief, download_url))
		{
			SHA256 sha256;
			std::string exePath = SHA256::GetProcExePath();
			if (!exePath.empty())
			{
				std::string hash = sha256.CalculateFileHash(exePath);
				sha256_verified = true;
				if (hash == brief)
				{
					allow_driver_init = true;
					if (!driver_init_done && allow_driver_init)
					{
						driver = new c_driver();
						driver_init_done = true;
					}
				}
			}
			else
			{
				safe_exit();
			}
		}
	}
	else if (!driver_init_done && allow_driver_init)
	{
		driver = new c_driver();
		driver_init_done = true;
	}

	static bool first_time = true;
	if (first_time)
	{
		ImVec2 center_pos;
		center_pos.x = (resolution_information.ScreenWidth - 800.f) / 2.f;
		center_pos.y = (resolution_information.ScreenHeiht - 580.f) / 2.f;
		ImGui::SetNextWindowPos(center_pos, ImGuiCond_Once);
		ImGui::SetNextWindowSize(ImVec2(760.f, 820.f), ImGuiCond_Once);
		first_time = false;
	}

	if (imguiswitch_information.boolswitch[40])
	{
		static bool AimiDown = false;
		static ImVec2 AimiPos = {0, 0};
		if (ImGui::Begin("Aimi", &imguiswitch_information.boolswitch[40], ImGuiWindowFlags_NoDecoration | ImGuiWindowFlags_NoBackground))
		{
			if (ImGui::IsItemActive())
			{
				if (!AimiDown)
				{
					AimiDown = true;
					AimiPos = ImGui::GetWindowPos();
				}
			}
			else if (AimiDown)
			{
				AimiDown = false;
				if (AimiPos.x == ImGui::GetWindowPos().x && AimiPos.y == ImGui::GetWindowPos().y)
				{
					imguiswitch_information.boolswitch[20] = !imguiswitch_information.boolswitch[20];
				}
			}
			if (imguiswitch_information.boolswitch[20])
			{
				ImGui::Image(noaimi_icon, ImVec2{110, 110}, ImVec2{0, 0}, ImVec2{1, 1});
			}
			else if (!imguiswitch_information.boolswitch[20])
			{
				ImGui::Image(yeaimi_icon, ImVec2{110, 110}, ImVec2{0, 0}, ImVec2{1, 1});
			}
		}
		ImGui::End();
	}
	if ((!driver || driver->get_fd() <= 0) && currentTab != 4 && allow_driver_init)
	{
		currentTab = 5;
	}
	if (ImGuiWindowDisplay && ShutImGuiProcess)
	{
		/*if (!buffer[0])
		{
			safe_exit();
			return;
		}*/
		ImGuiWindowFlags window_flags = ImGuiWindowFlags_NoCollapse | ImGuiWindowFlags_NoNav;
		if (ImGui::Begin(("Canary - " + std::string(buffer)).c_str(), &ImGuiWindowDisplay, window_flags))
		{
			ImVec2 closeButtonPos = ImVec2(ImGui::GetWindowWidth() - 25, 5);
			ImGui::SetCursorPos(closeButtonPos);
			if (ImGui::Button("X", ImVec2(20, 20)))
			{
				ImGuiWindowDisplay = false;
			}
			ImGui::Spacing();
			ImGui::Spacing();
			ImGui::Spacing();
			ImGui::Spacing();
			ImGui::Spacing();
			ImGui::Spacing();
			float window_width = ImGui::GetWindowWidth();
			float padding = 20.0f;
			float combo_width = window_width - (padding * 2);
			if (!initializedraw)
			{
				float buttonWidth = combo_width;
				ImGui::SetCursorPosX(padding);
				if (ImGui::Button("开启绘制", {buttonWidth, 40.f}))
				{
					initializedraw = InitializeDrawing();
				}
			}
			else
			{
				float buttonWidth = combo_width;
				ImGui::SetCursorPosX(padding);
				if (ImGui::Button("退出绘制", {buttonWidth, 40.f}))
				{
					exit(0);
					ShutImGuiProcess = false;
				}
			}
			ImGui::Spacing();
			ImGui::Separator();
			ImGui::Spacing();
			const char *menu_items[] = {
				"绘制面板",
				"绘制设置",
				"物资面板",
				"触摸自瞄",
				"枪械后座",
				"驱动面板"};
			ImGui::SetCursorPosX(padding);
			ImGui::PushItemWidth(combo_width);
			ImGui::Combo("##MenuSelect", &currentTab, menu_items, IM_ARRAYSIZE(menu_items));
			ImGui::PopItemWidth();
			ImGui::Spacing();
			ImGui::Separator();
			ImGui::Spacing();
			ImGui::BeginChild("Content", ImVec2(0, -1), true);
			switch (currentTab)
			{
			case 0: // 绘制面板
			{
				// 使用列数来控制每行显示的复选框数量
				const int columns = 5; // 每行显示5个复选框
				float checkboxWidth = (ImGui::GetContentRegionAvail().x - ImGui::GetStyle().ItemSpacing.x * (columns - 1)) / columns;
				int itemCount = 0;

				// 主页面信息
				if (Pid > 0)
				{
					ImGui::Text("游戏进程: %d | 游戏基址: %lX | WorldAddress: %lX", Pid, ModulesBase[0], WorldAddress);
				}
				ImGui::TextWrapped("无法触摸退出绘制重新执行！！");
				ImGui::TextWrapped("新增海岛密钥物资");
				ImGui::Text("设备分辨率: %dx%d", resolution_information.ScreenWidth, resolution_information.ScreenHeiht);
				ImGui::Text("ImGuiFrames: %0.2fFPS %0.2fms", ImGui::GetIO().Framerate, 1000.f / ImGui::GetIO().Framerate);
				ImGui::Text("自身武器: %d, 自身动作: %d, 自身队伍: %d,自身开火: %d,自身开镜: %d,Fov: %0.2f", 自身武器, 自身动作, OwnTeam, IsFire, bIsGunADS, Fov);

				float sliderWidth = combo_width * 0.7f; // 使用70%的宽度给滑条
				ImGui::SetCursorPosX(padding);
				ImGui::PushItemWidth(sliderWidth);
				if (ImGui::SliderInt("##帧率设置", &OneTimeFrame, 60, 144, "帧率设置 %d", ImGuiSliderFlags_AlwaysClamp))
				{
					SaveFile(".Canary");
				}
				ImGui::PopItemWidth();
				ImGui::SameLine();
				if (ImGui::Checkbox("物资调试", &imguiswitch_information.boolswitch[10]))
				{
					SaveFile(".Canary");
				}
				ImGui::Spacing();
				ImGui::Separator();
				ImGui::Spacing();

				// 人物绘制选项
				ImGui::Bullet();
				ImGui::SameLine();
				ImGui::Text("人物绘制");
				ImGui::BeginGroup();

				auto AddCheckbox = [&](const char *label, bool *value)
				{
					bool oldValue = *value;
					ImGui::PushItemWidth(checkboxWidth);
					ImGui::Checkbox(label, value);
					ImGui::PopItemWidth();
					if (oldValue != *value)
					{
						SaveFile(".Canary");
					}
					itemCount++;
					if (itemCount % columns != 0 && itemCount < 16)
						ImGui::SameLine();
				};

				AddCheckbox("骨骼", &imguiswitch_information.boolswitch[0]);
				AddCheckbox("方框", &imguiswitch_information.boolswitch[1]);
				AddCheckbox("射线", &imguiswitch_information.boolswitch[2]);
				AddCheckbox("血条", &imguiswitch_information.boolswitch[3]);
				AddCheckbox("名字", &imguiswitch_information.boolswitch[4]);
				AddCheckbox("距离", &imguiswitch_information.boolswitch[5]);
				AddCheckbox("手持", &imguiswitch_information.boolswitch[6]);
				AddCheckbox("背敌", &imguiswitch_information.boolswitch[7]);
				AddCheckbox("雷达", &imguiswitch_information.boolswitch[8]);
				AddCheckbox("可视", &imguiswitch_information.boolswitch[50]);
				AddCheckbox("忽机", &imguiswitch_information.boolswitch[9]);
				ImGui::EndGroup();
			}
			break;
			case 1: // 绘制设置
			{
				// 左侧设置
				ImGui::BeginChild("雷达设置", ImVec2(ImGui::GetWindowWidth() * 0.5f - 5.f, -1), true);
				if (ImGui::Combo("##血条样式", &imguiswitch_information.intswitch[0], "圆血条\0经典血条\0扁血条\0"))
				{
					SaveFile(".Canary");
				}
				if (ImGui::SliderFloat("##画笔粗细", &imguiswitch_information.floatswitch[0], 1.f, 5.f, "画笔粗细 %.2f", ImGuiSliderFlags_AlwaysClamp))
				{
					SaveFile(".Canary");
				}
				if (ImGui::SliderFloat("##雷达左右", &imguiswitch_information.floatswitch[1], -1000.f, 1000.f, "雷达左右 %.2f", ImGuiSliderFlags_AlwaysClamp))
				{
					SaveFile(".Canary");
				}
				if (ImGui::SliderFloat("##雷达上下", &imguiswitch_information.floatswitch[2], -1000.f, 1000.f, "雷达上下 %.2f", ImGuiSliderFlags_AlwaysClamp))
				{
					SaveFile(".Canary");
				}
				if (ImGui::SliderFloat("##雷达缩放", &imguiswitch_information.floatswitch[3], 0.f, 200.f, "雷达缩放 %.2f", ImGuiSliderFlags_AlwaysClamp))
				{
					SaveFile(".Canary");
				}
				if (ImGui::SliderFloat("##雷达大小", &imguiswitch_information.floatswitch[4], 100.f, 300.f, "雷达大小 %.2f", ImGuiSliderFlags_AlwaysClamp))
				{
					SaveFile(".Canary");
				}
				if (ImGui::SliderInt("##物资大小", &imguiswitch_information.intswitch[6], 10, 40, "物资大小 %d", ImGuiSliderFlags_AlwaysClamp))
				{
					SaveFile(".Canary");
				}
				if (ImGui::Button("删除配置", ImVec2(ImGui::GetWindowWidth() - 125.f, ImGui::GetFrameHeight())))
				{
					remove("/storage/emulated/0/Android/.Canary");
					remove("settings.conf");
					exit(0);
				}
				ImGui::EndChild();

				ImGui::SameLine();

				// 右侧颜色设置
				ImGui::BeginChild("颜色设置", ImVec2(ImGui::GetWindowWidth() * 0.5f - 5.f, -1), true);

				// 使用数组来简化代码
				const char *color_names[] = {
					"方框颜色", "骨骼颜色", "射线颜色", "距离颜色",
					"名称颜色", "队标颜色", "人机颜色", "被瞄颜色"};

				for (int i = 0; i < 8; i++)
				{
					float col[4] = {
						imguiswitch_information.colorswitch[i].Value.x,
						imguiswitch_information.colorswitch[i].Value.y,
						imguiswitch_information.colorswitch[i].Value.z,
						imguiswitch_information.colorswitch[i].Value.w};

					if (ImGui::ColorEdit4(color_names[i], col, ImGuiColorEditFlags_AlphaBar | ImGuiColorEditFlags_DisplayHex))
					{
						imguiswitch_information.colorswitch[i] = ImColor(col[0], col[1], col[2], col[3]);
						SaveFile(".Canary");
					}
				}

				ImGui::EndChild();
			}
			break;
			case 2: // 物资面板
			{
				ImGui::Text("物资设置");
				ImGui::Separator();
				ImGui::Spacing();

				const int columns = 4;
				float windowWidth = ImGui::GetWindowWidth();
				float itemWidth = (windowWidth - ImGui::GetStyle().ItemSpacing.x * (columns - 1)) / columns;
				float checkboxWidth = itemWidth * 0.6f;
				float colorWidth = itemWidth * 0.4f;
				float groupSpacing = 15.0f; // 组之间的间隔

				struct Item
				{
					const char *name;
					bool *value;
					int colorIndex;
				};

				Item items[] = {
					{"投掷", &imguiswitch_information.boolswitch[11], 8},
					{"载具", &imguiswitch_information.boolswitch[12], 9},
					{"防具", &imguiswitch_information.boolswitch[13], 10},
					{"道具", &imguiswitch_information.boolswitch[14], 11},
					{"盒子", &imguiswitch_information.boolswitch[15], 12},
					{"药品", &imguiswitch_information.boolswitch[16], 13},
					{"子弹", &imguiswitch_information.boolswitch[17], 14},
					{"步配", &imguiswitch_information.boolswitch[35], 21},
					{"倍镜", &imguiswitch_information.boolswitch[36], 22},
					{"地铁", &imguiswitch_information.boolswitch[19], 23},
					{"762", &imguiswitch_information.boolswitch[18], 15},
					{"556", &imguiswitch_information.boolswitch[30], 16},
					{"冲锋", &imguiswitch_information.boolswitch[31], 17},
					{"霰弹", &imguiswitch_information.boolswitch[32], 18},
					{"狙击", &imguiswitch_information.boolswitch[33], 19},
					{"其他", &imguiswitch_information.boolswitch[34], 20},
					{"油量", &imguiswitch_information.boolswitch[38], 9}};

				for (int i = 0; i < IM_ARRAYSIZE(items); i++)
				{
					if (i > 0 && i % columns == 0)
						ImGui::NewLine();

					ImGui::PushID(i);
					// 计算当前项的位置
					float startX = ImGui::GetCursorPosX();
					// 绘制复选框
					bool oldValue = *items[i].value;
					ImGui::PushItemWidth(checkboxWidth);
					ImGui::Checkbox(items[i].name, items[i].value);
					ImGui::PopItemWidth();
					if (oldValue != *items[i].value)
					{
						SaveFile(".Canary");
					}
					// 绘制颜色选择器
					ImGui::SameLine(startX + checkboxWidth);
					float col[4] = {
						imguiswitch_information.colorswitch[items[i].colorIndex].Value.x,
						imguiswitch_information.colorswitch[items[i].colorIndex].Value.y,
						imguiswitch_information.colorswitch[items[i].colorIndex].Value.z,
						imguiswitch_information.colorswitch[items[i].colorIndex].Value.w};
					ImGui::PushItemWidth(colorWidth);
					if (ImGui::ColorEdit4(("##" + std::string(items[i].name)).c_str(), col,
										  ImGuiColorEditFlags_AlphaBar | ImGuiColorEditFlags_NoInputs | ImGuiColorEditFlags_DisplayHex))
					{
						imguiswitch_information.colorswitch[items[i].colorIndex] = ImColor(col[0], col[1], col[2], col[3]);
						SaveFile(".Canary");
					}
					ImGui::PopItemWidth();
					// 如果不是最后一个元素，添加间距
					if (i % columns != columns - 1)
					{
						ImGui::SameLine();
						ImGui::Dummy(ImVec2(groupSpacing, 0)); // 添加组之间的间隔
						ImGui::SameLine();
					}
					ImGui::PopID();
				}
			}
			break;
			case 3: // 触摸自瞄
			{
				// 左侧面板
				ImGui::BeginChild("触摸自瞄", {((ImGui::GetWindowWidth() - 45.f) / 2.f - ImGui::GetStyle().ItemSpacing.x / 2.f), 0.f}, true);
				ImGui::Text("触摸自瞄");
				ImGui::SameLine();
				ImGui::TextDisabled("(?)");
				if (ImGui::IsItemHovered())
				{
					ImGui::BeginTooltip();
					ImGui::PushTextWrapPos(ImGui::GetFontSize() * 35.0f);
					ImGui::TextUnformatted("自瞄开关：总开关，控制所有自瞄功能\n喷子自瞄：启用对霰弹枪的自瞄功能\n狙击不瞄：禁用狙击枪的自瞄功能\n烟雾不瞄：禁用对烟雾中敌人的自瞄\n被瞄变色：自瞄锁定目标时改变颜色\n自瞄范围：显示自瞄感应范围\n触摸区域：显示虚拟触摸范围\n倒地优先/倒地不瞄：对倒地敌人的自瞄策略\n准心/距离最近：自瞄目标选择优先级\n软锁/硬锁模式：软锁非外设用户执行自瞄时可随意拖动 硬锁为死锁，执行自瞄时无法拖动");
					ImGui::PopTextWrapPos();
					ImGui::EndTooltip();
				}
				ImGui::Separator();
				ImGui::Spacing();

				if (ImGui::Checkbox("自瞄开关", &imguiswitch_information.boolswitch[20]))
				{
					SaveFile(".Canary");
				}
				if (ImGui::Checkbox("悬浮开关", &imguiswitch_information.boolswitch[40]))
				{
					SaveFile(".Canary");
				}
				if (ImGui::Checkbox("喷子自瞄", &imguiswitch_information.boolswitch[21]))
				{
					SaveFile(".Canary");
				}
				if (ImGui::Checkbox("狙击不瞄", &imguiswitch_information.boolswitch[22]))
				{
					SaveFile(".Canary");
				}
				if (ImGui::Checkbox("烟雾不瞄", &imguiswitch_information.boolswitch[23]))
				{
					SaveFile(".Canary");
				}
				if (ImGui::Checkbox("被瞄变色", &imguiswitch_information.boolswitch[24]))
				{
					SaveFile(".Canary");
				}
				if (ImGui::Checkbox("自瞄范围", &imguiswitch_information.boolswitch[25]))
				{
					SaveFile(".Canary");
				}
				if (ImGui::Checkbox("自瞄射线", &imguiswitch_information.boolswitch[26]))
				{
					SaveFile(".Canary");
				}
				if (ImGui::Checkbox("触摸区域", &imguiswitch_information.boolswitch[27]))
				{
					SaveFile(".Canary");
				}

				if (ImGui::Combo("##倒地优先", &imguiswitch_information.intswitch[2], "倒地瞄准\0倒地不瞄\0"))
				{
					SaveFile(".Canary");
				}
				if (ImGui::Combo("##选人优先", &imguiswitch_information.intswitch[3], "准心最近\0距离最近\0"))
				{
					SaveFile(".Canary");
				}
				if (ImGui::Combo("##人机调整", &imguiswitch_information.intswitch[4], "瞄准人机\0不瞄人机\0"))
				{
					SaveFile(".Canary");
				}
				if (ImGui::Combo("##范围调整", &imguiswitch_information.intswitch[5], "固定范围\0动态范围1\0动态范围2\0"))
				{
					SaveFile(".Canary");
				}
				if (ImGui::Combo("##触摸模式", &touch_information.touchLockMode, "软锁模式\0硬锁模式\0"))
				{
					SaveFile(".Canary");
					touch_information.TouchOrientationControl = true;
				}
				ImGui::EndChild();

				ImGui::SameLine();

				// 右侧面板
				ImGui::BeginChild("自瞄设置", {((ImGui::GetWindowWidth() - 45.f) / 2.f - ImGui::GetStyle().ItemSpacing.x / 2.f), 0.f}, true);
				ImGui::Text("自瞄设置");
				ImGui::SameLine();
				ImGui::TextDisabled("(?)");
				if (ImGui::IsItemHovered())
				{
					ImGui::BeginTooltip();
					ImGui::PushTextWrapPos(ImGui::GetFontSize() * 35.0f);
					ImGui::TextUnformatted("范围调整：控制自瞄的感应范围\n最小/最大范围：动态范围模式下的范围限制\n开镜距离：开镜时有效的最大自瞄距离\n腰射距离：腰射时有效的最大自瞄距离\n预判调整：目标移动预判程度\n血量调整：低于此血量才自瞄\n左右速度：水平移动速度，应为上下速度的一倍\n上下速度：垂直移动速度\n自瞄速度：总体移动速度，不能低于上下速度\n触摸左右/上下：触摸点位置\n触摸范围：虚拟触摸范围大小 可以开启触摸范围来显示当前触摸位置\n触摸帧数：开启喷子自瞄拿喷子去锁人调整触摸帧率 一点点调整测试 \n太低触摸屏幕会卡 太高会抖屏 只要触摸屏幕不卡 自瞄不晃就是合适的帧率");
					ImGui::PopTextWrapPos();
					ImGui::EndTooltip();
				}
				ImGui::Separator();
				ImGui::Spacing();

				if (imguiswitch_information.intswitch[5] == 0)
				{
					if (ImGui::SliderFloat("##范围调整", &imguiswitch_information.floatswitch[5], 50.0f, 250.f, "范围调整 %.2f", ImGuiSliderFlags_AlwaysClamp))
					{
						SaveFile(".Canary");
					}
				}
				else
				{
					if (ImGui::SliderFloat("##最小范围", &imguiswitch_information.floatswitch[9], 50.0f, 150.f, "最小范围 %.2f", ImGuiSliderFlags_AlwaysClamp))
					{
						SaveFile(".Canary");
					}
					if (ImGui::SliderFloat("##最大范围", &imguiswitch_information.floatswitch[10], 100.0f, 250.f, "最大范围 %.2f", ImGuiSliderFlags_AlwaysClamp))
					{
						SaveFile(".Canary");
					}
				}

				if (ImGui::SliderFloat("##开镜距离", &imguiswitch_information.floatswitch[6], 10.f, 250.f, "开镜距离 %.2f", ImGuiSliderFlags_AlwaysClamp))
				{
					SaveFile(".Canary");
				}
				if (ImGui::SliderFloat("##腰射距离", &imguiswitch_information.floatswitch[11], 0.0f, 100.f, "腰射距离 %.2f", ImGuiSliderFlags_AlwaysClamp))
				{
					SaveFile(".Canary");
				}
				if (ImGui::SliderFloat("##全局预判", &imguiswitch_information.floatswitch[7], 0.10f, 2.0f, "全局预判 %.2f", ImGuiSliderFlags_AlwaysClamp))
				{
					SaveFile(".Canary");
				}
				if (ImGui::SliderFloat("##近站预判", &imguiswitch_information.floatswitch[12], 0.10f, 3.0f, "近站预判 %.2f", ImGuiSliderFlags_AlwaysClamp))
				{
					SaveFile(".Canary");
				}
				if (ImGui::SliderFloat("##自瞄血量", &imguiswitch_information.floatswitch[8], 10.0f, 100.0f, "自瞄血量 %.2f", ImGuiSliderFlags_AlwaysClamp))
				{
					SaveFile(".Canary");
				}
				if (ImGui::SliderFloat("##左右速度", &touch_information.floatswitch[1], 0.01f, 0.20f, "左右速度 %.2f", ImGuiSliderFlags_AlwaysClamp))
				{
					SaveFile(".Canary");
				}
				if (ImGui::SliderFloat("##上下速度", &touch_information.floatswitch[2], 0.01f, 0.20f, "上下速度 %.2f", ImGuiSliderFlags_AlwaysClamp))
				{
					SaveFile(".Canary");
				}
				if (ImGui::SliderFloat("##自瞄速度", &touch_information.floatswitch[3], 0.01f, 0.50f, "自瞄速度 %.2f", ImGuiSliderFlags_AlwaysClamp))
				{
					touch_information.TouchOrientationControl = true;
					SaveFile(".Canary");
				}
				if (ImGui::SliderFloat("##触摸左右", &touch_information.TouchPoints.y, 0.f, 1.f, "触摸左右 %.2f", ImGuiSliderFlags_AlwaysClamp))
				{
					touch_information.TouchOrientationControl = true;
					SaveFile(".Canary");
				}
				if (ImGui::SliderFloat("##触摸上下", &touch_information.TouchPoints.x, 0.f, 1.f, "触摸上下 %.2f", ImGuiSliderFlags_AlwaysClamp))
				{
					touch_information.TouchOrientationControl = true;
					SaveFile(".Canary");
				}
				if (ImGui::SliderFloat("##触摸范围", &touch_information.TouchRadius, 0.01f, 0.08f, "触摸范围 %.2f", ImGuiSliderFlags_AlwaysClamp))
				{
					touch_information.TouchOrientationControl = true;
					SaveFile(".Canary");
				}
				int sliderValue = static_cast<int>(touch_information.floatswitch[0]);
				if (ImGui::SliderInt("##触摸帧数", &sliderValue, 300, 1200, "触摸帧数 %d", ImGuiSliderFlags_AlwaysClamp))
				{
					sliderValue = (sliderValue / 50) * 50;
					touch_information.floatswitch[0] = static_cast<float>(sliderValue);
					touch_information.TouchOrientationControl = true;
					SaveFile(".Canary");
				}

				ImGui::EndChild();
			}
			break;
			case 4: // 压枪与枪械设置
			{
				// 添加顶部切换选项
				static int pressureMode = 0;
				const char *pressureModes[] = {"全局压枪", "单独压枪"};

				// 居中显示模式选择Combo
				float windowWidth = ImGui::GetWindowWidth();
				float comboWidth = 200.0f;
				ImGui::SetCursorPosX((windowWidth - comboWidth) * 0.5f);
				ImGui::PushItemWidth(comboWidth);
				ImGui::Combo("##PressureMode", &pressureMode, pressureModes, IM_ARRAYSIZE(pressureModes));
				ImGui::PopItemWidth();

				ImGui::Spacing();
				ImGui::Separator();
				ImGui::Spacing();

				if (pressureMode == 0) // 全局压枪模式
				{
					// 显示原来的压枪设置内容
					ImGui::BeginChild("压枪设置", {(ImGui::GetWindowWidth() - 45.f) / 1.f - ImGui::GetStyle().ItemSpacing.x / 1.f, 0.f}, true);
					ImGui::Text("全局压枪设置");
					ImGui::SameLine();
					ImGui::TextDisabled("(?)");
					if (ImGui::IsItemHovered())
					{
						ImGui::BeginTooltip();
						ImGui::PushTextWrapPos(ImGui::GetFontSize() * 35.0f);
						ImGui::TextUnformatted("姿势压不住调姿势(站蹲趴)\n倍镜压不住调倍镜(机瞄红点二倍三倍四倍六倍八倍)\n单枪压不住调单枪\n+控制下压-控制上抬");
						ImGui::PopTextWrapPos();
						ImGui::EndTooltip();
					}
					ImGui::Separator();
					ImGui::Spacing();

					static int selectedOption = -1;
					const char *options[] = {
						"站着", "蹲着",
						"趴着", "腰射",
						"机瞄", "红点",
						"二倍", "三倍",
						"四倍", "六倍", "八倍"};
					float *values[] = {
						&imguiswitch_information.floatswitch[20], &imguiswitch_information.floatswitch[21],
						&imguiswitch_information.floatswitch[22], &imguiswitch_information.floatswitch[23],
						&imguiswitch_information.floatswitch[24], &imguiswitch_information.floatswitch[25],
						&imguiswitch_information.floatswitch[26], &imguiswitch_information.floatswitch[27],
						&imguiswitch_information.floatswitch[28], &imguiswitch_information.floatswitch[29],
						&imguiswitch_information.floatswitch[30]};

					// 计算每行显示的选项数量
					const int columns = 4; // 调整为每行4个选项
					float availWidth = ImGui::GetContentRegionAvail().x;
					float buttonWidth = (availWidth - ImGui::GetStyle().ItemSpacing.x * (columns - 1)) / columns;

					for (int i = 0; i < IM_ARRAYSIZE(options); i++)
					{
						ImGui::PushID(i);

						// 计算当前位置
						if (i % columns != 0)
							ImGui::SameLine();

						bool isSelected = (selectedOption == i);
						if (ImGui::RadioButton(options[i], isSelected))
						{
							selectedOption = i;
						}
						ImGui::SameLine();
						ImGui::Text("(%.1f)", *values[i]);

						// 换行逻辑调整为每4个选项
						if ((i + 1) % columns == 0 || i == IM_ARRAYSIZE(options) - 1)
						{
							if (i != IM_ARRAYSIZE(options) - 1 && (i + 1) % columns != 0)
							{
								// 确保最后一行剩余的位置也对齐
								int remainingInRow = columns - ((i + 1) % columns);
								for (int j = 0; j < remainingInRow; j++)
								{
									ImGui::NewLine();
								}
							}
							else
							{
								ImGui::NewLine();
							}
						}

						// 显示选中的选项详情
						if (isSelected)
						{
							ImGui::NewLine();
							float *currentValue = values[i];

							// 居中显示当前修改的内容
							char buffer[128];
							snprintf(buffer, sizeof(buffer), "正在修改: %s (当前值: %.1f)", options[i], *currentValue);
							float textWidth = ImGui::CalcTextSize(buffer).x;
							float windowWidth = ImGui::GetWindowWidth();
							ImGui::SetCursorPosX((windowWidth - textWidth) * 0.5f);
							ImGui::Text("%s", buffer);

							// 添加 +/- 按钮来调整值 - 居中显示
							float totalButtonWidth = 80 * 2 + ImGui::GetStyle().ItemSpacing.x; // 两个按钮加间距的总宽度
							ImGui::SetCursorPosX((windowWidth - totalButtonWidth) * 0.5f);

							if (ImGui::Button("-", ImVec2(80, 0)))
							{
								*currentValue = ImClamp(*currentValue - 0.1f, -1.0f, 4.0f);
								SaveFile(".Canary");
							}
							ImGui::SameLine();
							if (ImGui::Button("+", ImVec2(80, 0)))
							{
								*currentValue = ImClamp(*currentValue + 0.1f, -1.0f, 4.0f);
								SaveFile(".Canary");
							}

							ImGui::Separator();
							ImGui::Spacing();
						}
						ImGui::PopID();
					}
					ImGui::EndChild();
				}
				else // 单独压枪模式 (pressureMode == 1)
				{
					// 显示原来的枪械力度内容
					ImGui::BeginChild("枪械力度", {(ImGui::GetWindowWidth() - 45.f) / 1.f - ImGui::GetStyle().ItemSpacing.x / 1.f, 0.f}, true);
					ImGui::Text("单独枪械力度设置");
					ImGui::Separator();
					ImGui::Spacing();

					static int selectedWeapon = -1;
					static int recoilType = 0; // 0 for standing/crouching, 1 for prone

					const char *weapons[] = {
						"AKM", "M16A4", "SCAR-L", "M416", "Groza",
						"AUG", "QBZ", "M762", "Mk47", "G36C",
						"AC-VAL", "蜜獾", "法玛斯", "UZI", "UMP 5",
						"Vector", "汤姆逊", "野牛", "MP5K", "P90",
						"M249", "DP-28", "MG3", "PKM", "MG-36", "74u",
						"妹控", "ace", "js9", "mini4", "slr", "sks",
						"mk12", "m417", "mk20", "QBU", "ARX"};

					// 计算每行显示的武器数量
					const int columns = 5; // 全屏模式可以显示更多列
					float availWidth = ImGui::GetContentRegionAvail().x;
					float buttonWidth = (availWidth - ImGui::GetStyle().ItemSpacing.x * (columns - 1)) / columns;
					// 开始绘制网格布局
					for (int i = 0; i < IM_ARRAYSIZE(weapons); i++)
					{
						// 计算当前位置
						if (i % columns != 0)
							ImGui::SameLine();
						float cursorX = ImGui::GetCursorPosX();
						int column = i % columns;
						float expectedX = column * (buttonWidth + ImGui::GetStyle().ItemSpacing.x);
						// 确保位置对齐
						if (column > 0 && abs(cursorX - expectedX) > 0.1f)
							ImGui::SetCursorPosX(expectedX);
						ImGui::PushID(i);
						bool isSelected = (selectedWeapon == i);
						// 固定宽度的按钮
						ImGui::PushItemWidth(buttonWidth);
						if (ImGui::RadioButton(weapons[i], isSelected))
						{
							selectedWeapon = i;
						}
						ImGui::PopItemWidth();
						// 如果选中了武器,显示详细信息和编辑选项
						if (isSelected && (i == selectedWeapon))
						{
							// 确保详细信息显示在新的一行
							if ((i % columns) != columns - 1 && i < IM_ARRAYSIZE(weapons) - 1)
							{
								// 跳过当前行剩余的位置
								int remainingInRow = columns - 1 - (i % columns);
								for (int j = 0; j < remainingInRow; j++)
								{
									ImGui::SameLine();
									ImGui::Dummy(ImVec2(buttonWidth, 0));
								}
							}
							ImGui::Spacing();
							ImGui::Separator();
							ImGui::Spacing();
							// 显示当前武器的站蹲和趴下力度值 - 居中显示
							char valuesBuffer[128];
							snprintf(valuesBuffer, sizeof(valuesBuffer), "%s 站蹲力度: %.1f  趴下力度: %.1f",
									 weapons[i],
									 imguiswitch_information.StandingRecoil[i],
									 imguiswitch_information.ProneRecoil[i]);
							float valuesWidth = ImGui::CalcTextSize(valuesBuffer).x;
							float windowWidth = ImGui::GetWindowWidth();
							ImGui::SetCursorPosX((windowWidth - valuesWidth) * 0.5f);
							ImGui::Text("%s", valuesBuffer);
							// 使用单选按钮替代Combo
							ImGui::Spacing();
							float buttonWidth = 120.0f; // 全屏模式可以使用更宽的按钮
							float spacing = (windowWidth - buttonWidth * 2) / 3.0f;
							ImGui::SetCursorPosX(spacing);
							bool standSelected = (recoilType == 0);
							if (ImGui::RadioButton("站蹲力度", standSelected))
							{
								recoilType = 0;
							}
							ImGui::SameLine(spacing * 2 + buttonWidth);
							bool proneSelected = (recoilType == 1);
							if (ImGui::RadioButton("趴下力度", proneSelected))
							{
								recoilType = 1;
							}
							ImGui::Spacing();
							// 获取当前选择的力度值指针
							float *currentValue = recoilType == 0 ? &imguiswitch_information.StandingRecoil[i] : &imguiswitch_information.ProneRecoil[i];
							// 显示当前正在修改的内容 - 居中显示
							char buffer[128];
							snprintf(buffer, sizeof(buffer), "正在修改: %s %s (当前值: %.1f)",
									 weapons[i],
									 recoilType == 0 ? "站蹲力度" : "趴下力度",
									 *currentValue);

							float textWidth = ImGui::CalcTextSize(buffer).x;
							ImGui::SetCursorPosX((windowWidth - textWidth) * 0.5f);
							ImGui::Text("%s", buffer);

							// 添加 +/- 按钮来调整值 - 居中显示
							float totalButtonWidth = 80 * 2 + ImGui::GetStyle().ItemSpacing.x; // 两个按钮加间距的总宽度
							ImGui::SetCursorPosX((windowWidth - totalButtonWidth) * 0.5f);

							if (ImGui::Button("-", ImVec2(80, 0)))
							{
								*currentValue = ImClamp(*currentValue - 0.1f, -1.0f, 5.0f);
								SaveFile(".Canary");
							}
							ImGui::SameLine();
							if (ImGui::Button("+", ImVec2(80, 0)))
							{
								*currentValue = ImClamp(*currentValue + 0.1f, -1.0f, 5.0f);
								SaveFile(".Canary");
							}

							ImGui::Separator();
							ImGui::Spacing();
						}
						ImGui::PopID();
					}
					ImGui::EndChild();
				}
			}
			break;
			case 5: // 驱动面板
			{
				ImGui::BeginChild("驱动面板", ImVec2(0, -1), true);
				static char kernel_version[128] = {0};
				static std::string short_version;
				if (kernel_version[0] == 0)
				{
					get_kernel_version(kernel_version, sizeof(kernel_version));
					std::string full_version(kernel_version);
					size_t first_dot = full_version.find('.');
					if (first_dot != std::string::npos)
					{
						size_t second_dot = full_version.find('.', first_dot + 1);
						if (second_dot != std::string::npos)
						{
							size_t third_dot = full_version.find('.', second_dot + 1);
							if (third_dot != std::string::npos)
							{
								short_version = full_version.substr(0, third_dot);
							}
							else
							{
								size_t non_digit = full_version.find_first_not_of("0123456789", second_dot + 1);
								if (non_digit != std::string::npos)
								{
									short_version = full_version.substr(0, non_digit);
								}
								else
								{
									short_version = full_version;
								}
							}
						}
						else
						{
							short_version = full_version;
						}
					}
					else
					{
						short_version = full_version;
					}
				}

				ImGui::Text("内核版本: %s", short_version.c_str());
				if ((driver && driver->get_fd() > 0) || !allow_driver_init)
				{
					ImGui::SameLine();
					ImGui::TextColored(ImVec4(0.0f, 1.0f, 0.0f, 1.0f), "(已安装)");
				}

				ImGui::Spacing();
				ImGui::Separator();
				ImGui::Spacing();

				// 只在驱动未安装时且hash验证通过时显示安装选项
				if ((!driver || driver->get_fd() <= 0) && allow_driver_init)
				{
					std::string base_version = extract_base_version(kernel_version);
					std::vector<std::string> available_versions = get_available_versions(base_version.substr(0, base_version.find('.', base_version.find('.') + 1)));

					if (available_versions.empty())
					{
						ImGui::TextColored(ImVec4(1.0f, 0.0f, 0.0f, 1.0f), "错误: 未找到适用于当前内核版本的驱动");
					}
					else
					{
						ImGui::Text("选择要安装的驱动版本:");
						ImGui::Spacing();

						float buttonWidth = ImGui::GetContentRegionAvail().x * 0.8f;
						float buttonX = (ImGui::GetContentRegionAvail().x - buttonWidth) * 0.5f;

						for (const auto &version : available_versions)
						{
							ImGui::SetCursorPosX(buttonX);
							if (ImGui::Button(version.c_str(), ImVec2(buttonWidth, 35.f)))
							{
								if (geteuid() != 0)
								{
									ImGui::TextColored(ImVec4(1.0f, 0.0f, 0.0f, 1.0f), "错误: 需要ROOT权限运行");
								}
								else
								{
									bool success = load_driver_version(version);
									if (success)
									{
										std::this_thread::sleep_for(std::chrono::milliseconds(100)); // 等待驱动加载
										driver = new c_driver();
										if (driver->get_fd() <= 0)
										{
											ImGui::TextColored(ImVec4(1.0f, 0.0f, 0.0f, 1.0f), "错误: 驱动安装成功但无法打开设备");
										}
									}
								}
							}
							ImGui::Spacing();
						}
					}
				}

				ImGui::EndChild();
			}
			break;
			}

			ImGui::EndChild();
		}
		ImGui::End();
	}
}
