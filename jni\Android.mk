# 获取当前模块的目录
LOCAL_PATH := $(call my-dir)

# 清除之前的配置
include $(CLEAR_VARS)

# 模块名称
LOCAL_MODULE := CanaryAim

# 公共编译标志
COMMON_FLAGS += -std=c++17 -Wno-error=format-security -Wno-error=c++11-narrowing -fpermissive -w -fvisibility=hidden

# 混淆和优化标志
COMMON_FLAGS += -O2 -ffunction-sections -fdata-sections -fvisibility=hidden -fvisibility-inlines-hidden
COMMON_FLAGS += -fexceptions -frtti

# C 编译标志
LOCAL_CFLAGS += $(COMMON_FLAGS)

# C++ 编译标志
LOCAL_CPPFLAGS += $(COMMON_FLAGS)

# 添加本地 C/C++ 头文件目录
LOCAL_C_INCLUDES += \
	$(LOCAL_PATH)/include \
	$(LOCAL_PATH)/include/Font \
	$(LOCAL_PATH)/include/validate \
	$(LOCAL_PATH)/src/ImGuiELGS \
	$(LOCAL_PATH)/src/ImGuiTOOL \
	$(LOCAL_PATH)/include/ImGui \
	$(LOCAL_PATH)/include/Touch \
	$(LOCAL_PATH)/include/Assets \
	$(LOCAL_PATH)/src/qd \
	$(LOCAL_PATH)/src/res \
	$(LOCAL_PATH)/include/Curl

# 获取需要编译的源文件列表
FILE_LIST += $(wildcard $(LOCAL_PATH)/src/*.c*)
FILE_LIST += $(wildcard $(LOCAL_PATH)/src/res/*.c*)
FILE_LIST += $(wildcard $(LOCAL_PATH)/src/ImGuiTOOL/*.c*)
FILE_LIST += $(wildcard $(LOCAL_PATH)/src/qd/*.c*)
FILE_LIST += $(wildcard $(LOCAL_PATH)/src/ImGuiELGS/*.c*)
FILE_LIST += $(wildcard $(LOCAL_PATH)/include/ImGui/BackGround/*.c*)

# 添加源文件到 LOCAL_SRC_FILES
LOCAL_SRC_FILES += $(FILE_LIST:$(LOCAL_PATH)/%=%)

# 链接标志（增加strip选项来进一步混淆）
LOCAL_LDFLAGS += -Wl,--strip-all,--gc-sections

# 添加静态库
LOCAL_STATIC_LIBRARIES := libcurl_static libwolfssl_static libzstd_static libz_static

# 链接库和系统库（移除-lz因为我们使用静态库）
LOCAL_LDLIBS += -llog -landroid -lEGL -lGLESv1_CM -lGLESv2 -lGLESv3

# 构建可执行文件
include $(BUILD_EXECUTABLE)

# 导入libcurl静态库
include $(CLEAR_VARS)
LOCAL_MODULE := libcurl_static
LOCAL_SRC_FILES := lib/libcurl.a
include $(PREBUILT_STATIC_LIBRARY)

# 导入wolfssl静态库
include $(CLEAR_VARS)
LOCAL_MODULE := libwolfssl_static
LOCAL_SRC_FILES := lib/libwolfssl.a
include $(PREBUILT_STATIC_LIBRARY)

# 导入zstd静态库
include $(CLEAR_VARS)
LOCAL_MODULE := libzstd_static
LOCAL_SRC_FILES := lib/libzstd.a
include $(PREBUILT_STATIC_LIBRARY)

# 导入z静态库
include $(CLEAR_VARS)
LOCAL_MODULE := libz_static
LOCAL_SRC_FILES := lib/libz.a
include $(PREBUILT_STATIC_LIBRARY)
