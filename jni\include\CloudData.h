#ifndef CLOUD_DATA_H
#define CLOUD_DATA_H

#include <iostream>
#include <cstring>
#include <cstdlib>
#include <fstream>
#include <unistd.h>
#include <ctime>
#include <chrono>
#include <dlfcn.h>
#include <errno.h>
#include <dirent.h>
#include "Curl/curl.h"
#include "txyun.h"

// 云更新相关配置

// 下载配置
#define DOWNLOAD_TIMEOUT 0L    // 下载超时时间（0表示无限制）
#define CONNECTION_TIMEOUT 60L // 连接超时时间（秒）
#define LOW_SPEED_LIMIT 512L   // 最低速度限制（字节/秒）- 降低阈值
#define LOW_SPEED_TIME 120L    // 低速时间限制（秒）- 增加容忍时间
#define MAX_RETRY_COUNT 5      // 最大重试次数 - 增加重试次数
#define BUFFER_SIZE 131072     // 128KB 缓冲区 - 增大缓冲区

// 错误码定义
#define ERROR_NETWORK 1
#define ERROR_FILE_ACCESS 2
#define ERROR_VERSION_FORMAT 3
#define ERROR_DOWNLOAD 4
#define ERROR_FILE_PERMISSION 5
#define ERROR_404 6

class CloudData
{
private:
    // 回调函数，用于接收HTTP响应数据
    static size_t WriteCallback(void *contents, size_t size, size_t nmemb, std::string *userp)
    {
        userp->append((char *)contents, size * nmemb);
        return size * nmemb;
    }

    // 文件写入回调函数，带缓冲优化
    static size_t WriteFileCallback(void *contents, size_t size, size_t nmemb, FILE *fp)
    {
        size_t written = fwrite(contents, size, nmemb, fp);
        fflush(fp); // 强制刷新缓冲区，确保数据写入磁盘
        return written;
    }

    // 进度回调函数
    static int ProgressCallback(void *clientp, double dltotal, double dlnow, double ultotal, double ulnow)
    {
        static double last_dlnow = 0;
        static auto last_time = std::chrono::steady_clock::now();
        static bool first_call = true;

        if (dltotal > 0)
        {
            int percentage = (int)((dlnow / dltotal) * 100);

            // 计算下载速度
            auto current_time = std::chrono::steady_clock::now();
            double speed_kbps = 0;

            if (!first_call)
            {
                auto time_diff = std::chrono::duration_cast<std::chrono::milliseconds>(current_time - last_time).count();
                if (time_diff > 500) // 每500毫秒更新一次速度计算
                {
                    double data_diff = dlnow - last_dlnow;
                    speed_kbps = (data_diff / 1024) / (time_diff / 1000.0); // KB/s

                    last_dlnow = dlnow;
                    last_time = current_time;
                }
                else
                {
                    // 如果时间间隔太短，使用上次计算的速度
                    if (time_diff > 0)
                    {
                        double data_diff = dlnow - last_dlnow;
                        speed_kbps = (data_diff / 1024) / (time_diff / 1000.0);
                    }
                }
            }
            else
            {
                first_call = false;
                last_dlnow = dlnow;
                last_time = current_time;
            }

            std::cout << "\r\033[35;1m[+] 下载进度: " << percentage << "% ("
                      << (long)(dlnow / 1024) << "KB/" << (long)(dltotal / 1024) << "KB) "
                      << "下载速度: " << (long)speed_kbps << "KB/s\033[0m" << std::flush;
        }
        return 0;
    }

    // 配置CURL选项以支持大文件下载
    static void ConfigureCurlForLargeFile(CURL *curl)
    {
        // 设置超时选项
        curl_easy_setopt(curl, CURLOPT_TIMEOUT, DOWNLOAD_TIMEOUT);
        curl_easy_setopt(curl, CURLOPT_CONNECTTIMEOUT, CONNECTION_TIMEOUT);
        curl_easy_setopt(curl, CURLOPT_LOW_SPEED_LIMIT, LOW_SPEED_LIMIT);
        curl_easy_setopt(curl, CURLOPT_LOW_SPEED_TIME, LOW_SPEED_TIME);

        // 设置缓冲区大小
        curl_easy_setopt(curl, CURLOPT_BUFFERSIZE, BUFFER_SIZE);

        // 启用TCP保活 - 优化大文件下载
        curl_easy_setopt(curl, CURLOPT_TCP_KEEPALIVE, 1L);
        curl_easy_setopt(curl, CURLOPT_TCP_KEEPIDLE, 60L);  // 减少空闲时间
        curl_easy_setopt(curl, CURLOPT_TCP_KEEPINTVL, 30L); // 减少探测间隔

        // 跟随重定向
        curl_easy_setopt(curl, CURLOPT_FOLLOWLOCATION, 1L);
        curl_easy_setopt(curl, CURLOPT_MAXREDIRS, 5L);

        // 设置用户代理
        curl_easy_setopt(curl, CURLOPT_USERAGENT, "CloudData/2.0");

        // 启用压缩
        curl_easy_setopt(curl, CURLOPT_ACCEPT_ENCODING, "");

        // 禁用信号处理（多线程安全）
        curl_easy_setopt(curl, CURLOPT_NOSIGNAL, 1L);

        // 额外的大文件下载优化
        curl_easy_setopt(curl, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1); // 使用HTTP/1.1
        curl_easy_setopt(curl, CURLOPT_FRESH_CONNECT, 0L);                   // 允许连接复用
        curl_easy_setopt(curl, CURLOPT_FORBID_REUSE, 0L);                    // 允许连接复用
        curl_easy_setopt(curl, CURLOPT_MAXCONNECTS, 5L);                     // 最大连接数

        // 设置更大的接收缓冲区
        curl_easy_setopt(curl, CURLOPT_SOCKOPTFUNCTION, NULL);
        curl_easy_setopt(curl, CURLOPT_SOCKOPTDATA, NULL);
    }

    // 获取当前运行的SO库路径
    static std::string GetCurrentLibraryPath()
    {
        Dl_info dlInfo;
        if (dladdr((void *)&CloudData::GetCurrentLibraryPath, &dlInfo) == 0)
        {
            return "";
        }
        return std::string(dlInfo.dli_fname);
    }

    // 从当前路径推导出lib目录中的SO文件路径
    static std::string GetLibraryPath()
    {
        std::string currentPath = GetCurrentLibraryPath();
        if (currentPath.empty())
        {
            return "";
        }

        // 当前路径类似: /data/data/com.package/files/CanaryAim.so
        // 需要转换为: /data/app/.../lib/arm64/CanaryAim.so

        // 提取文件名
        size_t pos = currentPath.find_last_of("/");
        std::string fileName = (pos != std::string::npos) ? currentPath.substr(pos + 1) : currentPath;

        // 构建lib路径 - 需要根据实际包名调整
        std::string libPath = "/data/app";

        // 遍历查找正确的lib目录
        DIR *dir = opendir(libPath.c_str());
        if (dir)
        {
            struct dirent *entry;
            while ((entry = readdir(dir)))
            {
                if (entry->d_type == DT_DIR && strstr(entry->d_name, "com.") != nullptr)
                {
                    std::string appDir = libPath + "/" + entry->d_name;
                    std::string libDir = appDir + "/lib/arm64";
                    std::string targetFile = libDir + "/" + fileName;

                    // 检查文件是否存在
                    if (access(targetFile.c_str(), F_OK) == 0)
                    {
                        closedir(dir);
                        return targetFile;
                    }
                }
            }
            closedir(dir);
        }

        return "";
    }

    // 获取目录路径
    static std::string GetDirectoryPath(const std::string &fullPath)
    {
        size_t pos = fullPath.find_last_of("/\\");
        return (pos != std::string::npos) ? fullPath.substr(0, pos) : "";
    }

    // 带重试机制的下载函数
    static bool DownloadWithRetry(const std::string &url, const std::string &filename, int max_retries = MAX_RETRY_COUNT)
    {
        for (int retry = 0; retry < max_retries; retry++)
        {
            if (retry > 0)
            {
                std::cout << "\n\033[33;1m[!] 重试下载 (" << retry + 1 << "/" << max_retries << ")...\033[0m" << std::endl;
                sleep(5 + retry * 2); // 递增等待时间：5秒、7秒、9秒...
            }

            CURL *curl = curl_easy_init();
            if (!curl)
            {
                std::cerr << "\033[31;1m[-] 初始化CURL失败\033[0m" << std::endl;
                continue;
            }

            FILE *fp = fopen(filename.c_str(), "wb");
            if (!fp)
            {
                std::cerr << "\033[31;1m[-] 无法创建下载文件\033[0m" << std::endl;
                curl_easy_cleanup(curl);
                continue;
            }

            // 配置CURL选项
            curl_easy_setopt(curl, CURLOPT_URL, url.c_str());
            curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, WriteFileCallback);
            curl_easy_setopt(curl, CURLOPT_WRITEDATA, fp);
            curl_easy_setopt(curl, CURLOPT_NOPROGRESS, 0L);
            curl_easy_setopt(curl, CURLOPT_PROGRESSFUNCTION, ProgressCallback);

            // 应用大文件下载配置
            ConfigureCurlForLargeFile(curl);

            // 执行下载
            CURLcode res = curl_easy_perform(curl);
            long http_code = 0;
            curl_easy_getinfo(curl, CURLINFO_RESPONSE_CODE, &http_code);

            fclose(fp);
            curl_easy_cleanup(curl);

            if (res == CURLE_OK && http_code == 200)
            {
                return true;
            }
            else
            {
                std::cerr << "\n\033[31;1m[-] 下载失败：" << curl_easy_strerror(res)
                          << " (HTTP: " << http_code << ")\033[0m" << std::endl;
                remove(filename.c_str()); // 删除不完整的文件
            }
        }

        std::cerr << "\033[31;1m[-] 下载失败，已达到最大重试次数\033[0m" << std::endl;
        return false;
    }

public:
    static bool CheckUpdate()
    {
        char version[1024] = {0};      // 版本号
        char hash[1024] = {0};         // 哈希值
        char download_url[1024] = {0}; // 下载链接
        // 使用腾讯云分享链接获取版本号、哈希和下载链接
        if (!getTxyunVersionHashUrl(TXYUN_URL, version, hash, download_url))
        {
            std::cerr << "\033[31;1m[-] 获取更新信息失败\033[0m" << std::endl;
            exit(ERROR_NETWORK);
        }
        if (strlen(version) == 0 || strlen(hash) == 0 || strlen(download_url) == 0)
        {
            std::cerr << "\033[31;1m[-] 更新信息格式错误\033[0m" << std::endl;
            exit(ERROR_VERSION_FORMAT);
        }
        // 如果远程版本号大于当前版本
        if (strcmp(version, TXYUN_VERSION) > 0)
        {
            std::cout << "\033[35;1m[+] 发现新版本 " << version << "，正在更新...\033[0m" << std::endl;

            // 获取当前运行的程序路径
            std::string currentPath = GetCurrentLibraryPath();

            std::cout << "\033[36;1m[*] 当前运行路径: " << currentPath << "\033[0m" << std::endl;

            if (currentPath.empty())
            {
                std::cerr << "\033[31;1m[-] 无法获取当前程序路径\033[0m" << std::endl;
                exit(ERROR_FILE_ACCESS);
            }

            std::string currentDir = GetDirectoryPath(currentPath);
            if (currentDir.empty())
            {
                std::cerr << "\033[31;1m[-] 无法获取当前目录路径\033[0m" << std::endl;
                exit(ERROR_FILE_ACCESS);
            }

            // 获取当前程序文件名
            size_t pos = currentPath.find_last_of("/");
            std::string currentFileName = (pos != std::string::npos) ? currentPath.substr(pos + 1) : currentPath;

            std::cout << "\033[36;1m[*] 当前程序文件名: " << currentFileName << "\033[0m" << std::endl;
            std::cout << "\033[36;1m[*] 更新目标目录: " << currentDir << "\033[0m" << std::endl;

            std::string new_file = currentDir + "/update.tmp";
            // 使用带重试机制的下载函数，使用从腾讯云获取的下载链接
            if (!DownloadWithRetry(std::string(download_url), new_file))
            {
                std::cerr << "\033[31;1m[-] 下载失败\033[0m" << std::endl;
                exit(ERROR_DOWNLOAD);
            }
            // 验证下载的文件大小
            std::ifstream file(new_file.c_str(), std::ios::binary | std::ios::ate);
            if (!file.is_open())
            {
                std::cerr << "\033[31;1m[-] 无法验证下载文件\033[0m" << std::endl;
                remove(new_file.c_str());
                exit(ERROR_FILE_ACCESS);
            }
            std::streamsize file_size = file.tellg();
            file.close();

            if (file_size < 1024)
            { // 文件太小，可能下载不完整
                std::cerr << "\033[31;1m[-] 下载文件大小异常，可能下载不完整\033[0m" << std::endl;
                remove(new_file.c_str());
                exit(ERROR_DOWNLOAD);
            }
            // 设置执行权限
            if (chmod(new_file.c_str(), 0755) != 0)
            {
                std::cerr << "\033[31;1m[-] 设置文件权限失败\033[0m" << std::endl;
                remove(new_file.c_str());
                exit(ERROR_FILE_PERMISSION);
            }

            // 直接替换当前运行的程序文件
            if (rename(new_file.c_str(), currentPath.c_str()) != 0)
            {
                std::cerr << "\033[31;1m[-] 更新文件失败: " << strerror(errno) << "\033[0m" << std::endl;
                remove(new_file.c_str());
                exit(ERROR_FILE_ACCESS);
            }

            // 设置正确的权限
            if (chmod(currentPath.c_str(), 0755) != 0)
            {
                std::cerr << "\033[33;1m[!] 设置文件权限失败: " << strerror(errno) << "\033[0m" << std::endl;
            }

            std::cout << "\n\033[35;1m[+] 程序更新完成，请重新运行程序\033[0m" << std::endl;
            return true;
        }
        else
        {
            // std::cout << "\033[32;1m[+] 当前已是最新版本\033[0m" << std::endl;
        }

        return false;
    }
};

#endif // CLOUD_DATA_H